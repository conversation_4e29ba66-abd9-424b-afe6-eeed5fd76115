export { isAstroComponentFactory } from './factory.js';
export type { AstroComponentFactory } from './factory.js';
export { createHeadAndContent, isHeadAndContent } from './head-and-content.js';
export { createAstroComponentInstance, isAstroComponentInstance } from './instance.js';
export type { AstroComponentInstance } from './instance.js';
export { isRenderTemplateResult, renderTemplate } from './render-template.js';
export { renderToReadableStream, renderToString } from './render.js';
