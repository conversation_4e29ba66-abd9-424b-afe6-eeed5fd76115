---
import type { ShareButtonProps } from '@types/index';

export interface Props extends ShareButtonProps {}

const { title, url, className = '' } = Astro.props;
---

<div class={`share-button ${className}`}>
  <button 
    class="share-trigger"
    aria-label="分享作品"
    data-share-title={title}
    data-share-url={url}
  >
    <span class="share-icon" aria-hidden="true">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="18" cy="5" r="3"/>
        <circle cx="6" cy="12" r="3"/>
        <circle cx="18" cy="19" r="3"/>
        <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"/>
        <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"/>
      </svg>
    </span>
    <span class="share-text">分享</span>
  </button>

  <!-- Share Menu -->
  <div class="share-menu" data-share-menu>
    <div class="share-options">
      <button class="share-option" data-share-method="native" aria-label="使用系统分享">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
          <polyline points="16,6 12,2 8,6"/>
          <line x1="12" y1="2" x2="12" y2="15"/>
        </svg>
        <span>系统分享</span>
      </button>

      <button class="share-option" data-share-method="copy" aria-label="复制链接">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
          <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
        </svg>
        <span>复制链接</span>
      </button>

      <button class="share-option" data-share-method="weibo" aria-label="分享到微博">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1.5 15c-2.5 0-4.5-1.5-4.5-3.5s2-3.5 4.5-3.5 4.5 1.5 4.5 3.5-2 3.5-4.5 3.5z"/>
        </svg>
        <span>微博</span>
      </button>

      <button class="share-option" data-share-method="wechat" aria-label="分享到微信">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M8.5 12c-.83 0-1.5-.67-1.5-1.5S7.67 9 8.5 9s1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm7 0c-.83 0-1.5-.67-1.5-1.5S14.67 9 15.5 9s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/>
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-1.12.23-2.18.65-3.15C6.1 8.4 8.8 8 12 8s5.9.4 7.35.85c.42.97.65 2.03.65 3.15 0 4.41-3.59 8-8 8z"/>
        </svg>
        <span>微信</span>
      </button>
    </div>
  </div>

  <!-- Copy Success Message -->
  <div class="copy-success" data-copy-success>
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <polyline points="20,6 9,17 4,12"/>
    </svg>
    <span>链接已复制</span>
  </div>
</div>

<style>
  .share-button {
    position: relative;
    display: inline-block;
  }

  .share-trigger {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: 1px solid var(--color-border);
    border-radius: 2rem;
    padding: 0.5rem 1rem;
    font-family: var(--font-sans);
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
  }

  .share-trigger:hover {
    border-color: var(--color-accent);
    color: var(--color-accent);
    background: rgba(184, 134, 11, 0.05);
  }

  .share-trigger:active {
    transform: scale(0.95);
  }

  .share-icon {
    display: flex;
    align-items: center;
    transition: transform var(--transition-fast);
  }

  .share-trigger:hover .share-icon {
    transform: scale(1.1);
  }

  .share-text {
    font-weight: 500;
  }

  /* Share Menu */
  .share-menu {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 0.5rem;
    background: white;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-lightbox);
    padding: 0.5rem;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    z-index: 1000;
    min-width: 200px;
  }

  .share-menu.open {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-4px);
  }

  .share-options {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .share-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: none;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-family: var(--font-sans);
    font-size: 0.875rem;
    color: var(--color-text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: left;
    width: 100%;
  }

  .share-option:hover {
    background: var(--color-divider);
    color: var(--color-accent);
  }

  .share-option svg {
    flex-shrink: 0;
    color: var(--color-text-secondary);
    transition: color var(--transition-fast);
  }

  .share-option:hover svg {
    color: var(--color-accent);
  }

  /* Copy Success Message */
  .copy-success {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 0.5rem;
    background: var(--color-success);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    z-index: 1001;
    white-space: nowrap;
  }

  .copy-success.show {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-4px);
  }

  /* Responsive */
  @media (max-width: 767px) {
    .share-trigger {
      padding: 0.75rem 1.25rem;
      font-size: 1rem;
    }

    .share-menu {
      position: fixed;
      bottom: 2rem;
      left: 1rem;
      right: 1rem;
      transform: none;
      margin-bottom: 0;
      min-width: auto;
    }

    .share-menu.open {
      transform: translateY(-4px);
    }

    .share-option {
      padding: 1rem;
      font-size: 1rem;
    }
  }

  /* Accessibility */
  .share-trigger:focus {
    outline: 2px solid var(--color-accent);
    outline-offset: 2px;
  }

  .share-option:focus {
    outline: 2px solid var(--color-accent);
    outline-offset: -2px;
  }

  @media (prefers-reduced-motion: reduce) {
    .share-trigger,
    .share-icon,
    .share-menu,
    .share-option,
    .copy-success {
      transition: none;
    }

    .share-trigger:active {
      transform: none;
    }

    .share-trigger:hover .share-icon {
      transform: none;
    }
  }
</style>

<script>
  // Share functionality
  document.addEventListener('DOMContentLoaded', function() {
    const shareButtons = document.querySelectorAll('.share-button');
    
    shareButtons.forEach(shareButton => {
      const trigger = shareButton.querySelector('.share-trigger');
      const menu = shareButton.querySelector('.share-menu');
      const copySuccess = shareButton.querySelector('.copy-success');
      const options = shareButton.querySelectorAll('.share-option');
      
      const title = trigger.getAttribute('data-share-title');
      const url = trigger.getAttribute('data-share-url');
      
      // Toggle share menu
      trigger.addEventListener('click', function(e) {
        e.stopPropagation();
        
        // Close other open menus
        document.querySelectorAll('.share-menu.open').forEach(otherMenu => {
          if (otherMenu !== menu) {
            otherMenu.classList.remove('open');
          }
        });
        
        menu.classList.toggle('open');
      });
      
      // Handle share options
      options.forEach(option => {
        option.addEventListener('click', async function() {
          const method = this.getAttribute('data-share-method');
          
          switch (method) {
            case 'native':
              if (navigator.share) {
                try {
                  await navigator.share({
                    title: title,
                    url: url
                  });
                } catch (error) {
                  if (error.name !== 'AbortError') {
                    console.error('Native share failed:', error);
                    fallbackCopyToClipboard();
                  }
                }
              } else {
                fallbackCopyToClipboard();
              }
              break;
              
            case 'copy':
              fallbackCopyToClipboard();
              break;
              
            case 'weibo':
              const weiboUrl = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`;
              window.open(weiboUrl, '_blank', 'width=600,height=400');
              break;
              
            case 'wechat':
              // For WeChat, we'll copy the link and show instructions
              fallbackCopyToClipboard();
              showWeChatInstructions();
              break;
          }
          
          menu.classList.remove('open');
        });
      });
      
      // Copy to clipboard function
      async function fallbackCopyToClipboard() {
        try {
          if (navigator.clipboard) {
            await navigator.clipboard.writeText(url);
          } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = url;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            document.execCommand('copy');
            textArea.remove();
          }
          
          showCopySuccess();
        } catch (error) {
          console.error('Copy failed:', error);
        }
      }
      
      // Show copy success message
      function showCopySuccess() {
        copySuccess.classList.add('show');
        setTimeout(() => {
          copySuccess.classList.remove('show');
        }, 2000);
      }
      
      // Show WeChat instructions
      function showWeChatInstructions() {
        const instructions = document.createElement('div');
        instructions.className = 'wechat-instructions';
        instructions.innerHTML = `
          <div class="instructions-content">
            <p>链接已复制，请在微信中粘贴分享</p>
            <button class="close-instructions">知道了</button>
          </div>
        `;
        instructions.style.cssText = `
          position: fixed;
          inset: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10000;
        `;
        
        const content = instructions.querySelector('.instructions-content');
        content.style.cssText = `
          background: white;
          padding: 2rem;
          border-radius: 1rem;
          text-align: center;
          max-width: 300px;
          margin: 1rem;
        `;
        
        const closeButton = instructions.querySelector('.close-instructions');
        closeButton.style.cssText = `
          background: var(--color-accent);
          color: white;
          border: none;
          padding: 0.75rem 1.5rem;
          border-radius: 0.5rem;
          margin-top: 1rem;
          cursor: pointer;
        `;
        
        closeButton.addEventListener('click', () => {
          instructions.remove();
        });
        
        instructions.addEventListener('click', (e) => {
          if (e.target === instructions) {
            instructions.remove();
          }
        });
        
        document.body.appendChild(instructions);
      }
    });
    
    // Close share menus when clicking outside
    document.addEventListener('click', function() {
      document.querySelectorAll('.share-menu.open').forEach(menu => {
        menu.classList.remove('open');
      });
    });
    
    // Close share menus on escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        document.querySelectorAll('.share-menu.open').forEach(menu => {
          menu.classList.remove('open');
        });
      }
    });
  });
</script>
