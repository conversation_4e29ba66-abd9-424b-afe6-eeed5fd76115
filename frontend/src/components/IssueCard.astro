---
import type { IssueCardProps } from '@types/index';
import { strapiUtils } from '@services/strapi';

export interface Props extends IssueCardProps {}

const { issue, className = '' } = Astro.props;

const coverImageUrl = strapiUtils.getMediaUrl(issue.coverImage);
const issueUrl = strapiUtils.getIssueUrl(issue.issueNumber);
const formattedDate = strapiUtils.formatDate(issue.publicationDate);
const worksCount = issue.works?.length || 0;
---

<article class={`issue-card ${className}`}>
  <a href={issueUrl} class="card-link" aria-label={`阅读第${issue.issueNumber}期：${issue.title}`}>
    <!-- Cover Image -->
    <div class="card-image">
      <img
        src={coverImageUrl}
        alt={issue.coverImage.alternativeText || `第${issue.issueNumber}期封面`}
        class="cover-image"
        loading="lazy"
        width={issue.coverImage.width}
        height={issue.coverImage.height}
      />
      <div class="image-overlay">
        <div class="issue-number">#{issue.issueNumber}</div>
      </div>
    </div>

    <!-- Card Content -->
    <div class="card-content">
      <div class="card-header">
        <h3 class="issue-title">{issue.title}</h3>
        <time class="publication-date" datetime={issue.publicationDate.toISOString()}>
          {formattedDate}
        </time>
      </div>

      <div class="card-meta">
        <span class="works-count">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
            <circle cx="8.5" cy="8.5" r="1.5"/>
            <path d="M21 15l-5-5L5 21l5-5"/>
          </svg>
          {worksCount} 作品
        </span>
        
        <span class="read-indicator">
          阅读
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M5 12h14M12 5l7 7-7 7"/>
          </svg>
        </span>
      </div>
    </div>
  </a>
</article>

<style>
  .issue-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-card);
    transition: all var(--transition-normal);
    height: 100%;
  }

  .issue-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-card-hover);
  }

  .card-link {
    display: block;
    text-decoration: none;
    color: inherit;
    height: 100%;
  }

  /* Card Image */
  .card-image {
    position: relative;
    aspect-ratio: 4/3;
    overflow: hidden;
  }

  .cover-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
  }

  .issue-card:hover .cover-image {
    transform: scale(1.05);
  }

  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.1) 0%,
      rgba(0, 0, 0, 0.3) 100%
    );
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    padding: 1rem;
  }

  .issue-number {
    background: rgba(184, 134, 11, 0.9);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-family: var(--font-mono);
    font-size: 0.875rem;
    font-weight: 600;
    letter-spacing: 0.05em;
    backdrop-filter: blur(10px);
  }

  /* Card Content */
  .card-content {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    flex: 1;
  }

  .card-header {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .issue-title {
    font-family: var(--font-serif);
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.3;
    color: var(--color-text-primary);
    margin: 0;
    transition: color var(--transition-fast);
  }

  .card-link:hover .issue-title {
    color: var(--color-accent);
  }

  .publication-date {
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    font-weight: 400;
  }

  /* Card Meta */
  .card-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid var(--color-divider);
  }

  .works-count {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--color-text-secondary);
  }

  .works-count svg {
    color: var(--color-accent);
  }

  .read-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--color-accent);
    font-weight: 500;
    transition: all var(--transition-fast);
  }

  .card-link:hover .read-indicator {
    color: var(--color-accent-dark);
    transform: translateX(4px);
  }

  .read-indicator svg {
    transition: transform var(--transition-fast);
  }

  .card-link:hover .read-indicator svg {
    transform: translateX(2px);
  }

  /* Responsive */
  @media (max-width: 767px) {
    .card-content {
      padding: 1rem;
    }

    .issue-title {
      font-size: 1.125rem;
    }

    .card-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
  }

  /* Accessibility */
  @media (prefers-reduced-motion: reduce) {
    .issue-card,
    .cover-image,
    .issue-title,
    .read-indicator,
    .read-indicator svg {
      transition: none;
    }

    .issue-card:hover {
      transform: none;
    }

    .issue-card:hover .cover-image {
      transform: none;
    }
  }

  /* Focus styles */
  .card-link:focus {
    outline: 2px solid var(--color-accent);
    outline-offset: 2px;
  }

  .card-link:focus .issue-card {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card-hover);
  }
</style>
