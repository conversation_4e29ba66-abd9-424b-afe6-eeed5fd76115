---
import type { LikeButtonProps } from '@types/index';

export interface Props extends LikeButtonProps {}

const { workId, initialLikes, className = '' } = Astro.props;
---

<button 
  class={`like-button ${className}`}
  data-work-id={workId}
  data-initial-likes={initialLikes}
  aria-label={`为作品点赞，当前${initialLikes}个赞`}
>
  <span class="like-icon" aria-hidden="true">
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
    </svg>
  </span>
  <span class="like-count">{initialLikes}</span>
  <span class="loading-spinner" aria-hidden="true">
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M21 12a9 9 0 11-6.219-8.56"/>
    </svg>
  </span>
</button>

<style>
  .like-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: 1px solid var(--color-border);
    border-radius: 2rem;
    padding: 0.5rem 1rem;
    font-family: var(--font-sans);
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    min-width: 80px;
    justify-content: center;
  }

  .like-button:hover {
    border-color: var(--color-accent);
    color: var(--color-accent);
    background: rgba(184, 134, 11, 0.05);
  }

  .like-button:active {
    transform: scale(0.95);
  }

  /* Liked state */
  .like-button.liked {
    background: var(--color-accent);
    border-color: var(--color-accent);
    color: white;
  }

  .like-button.liked:hover {
    background: var(--color-accent-dark);
    border-color: var(--color-accent-dark);
  }

  .like-button.liked .like-icon svg {
    fill: currentColor;
  }

  /* Loading state */
  .like-button.loading {
    pointer-events: none;
    opacity: 0.7;
  }

  .like-button.loading .like-icon,
  .like-button.loading .like-count {
    opacity: 0.5;
  }

  .loading-spinner {
    display: none;
    position: absolute;
    animation: spin 1s linear infinite;
  }

  .like-button.loading .loading-spinner {
    display: block;
  }

  /* Icons */
  .like-icon {
    display: flex;
    align-items: center;
    transition: transform var(--transition-fast);
  }

  .like-button:hover .like-icon {
    transform: scale(1.1);
  }

  .like-button.liked .like-icon {
    animation: pulse-like 0.6s ease-in-out;
  }

  .like-count {
    font-weight: 500;
    min-width: 1.5rem;
    text-align: center;
  }

  /* Animations */
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes pulse-like {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.2);
    }
  }

  /* Accessibility */
  .like-button:focus {
    outline: 2px solid var(--color-accent);
    outline-offset: 2px;
  }

  @media (prefers-reduced-motion: reduce) {
    .like-button,
    .like-icon,
    .loading-spinner {
      transition: none;
      animation: none;
    }

    .like-button:active {
      transform: none;
    }

    .like-button:hover .like-icon {
      transform: none;
    }
  }

  /* Responsive */
  @media (max-width: 767px) {
    .like-button {
      padding: 0.75rem 1.25rem;
      font-size: 1rem;
      min-width: 90px;
    }
  }
</style>

<script>
  // Like button functionality
  document.addEventListener('DOMContentLoaded', function() {
    const likeButtons = document.querySelectorAll('.like-button');
    
    // Check for previously liked works in localStorage
    const likedWorks = JSON.parse(localStorage.getItem('likedWorks') || '[]');
    
    likeButtons.forEach(button => {
      const workId = button.getAttribute('data-work-id');
      
      // Set initial liked state
      if (likedWorks.includes(workId)) {
        button.classList.add('liked');
        button.setAttribute('aria-label', `取消点赞，当前${button.querySelector('.like-count').textContent}个赞`);
      }
      
      button.addEventListener('click', async function() {
        if (this.classList.contains('loading')) return;
        
        const workId = this.getAttribute('data-work-id');
        const isLiked = this.classList.contains('liked');
        const action = isLiked ? 'unlike' : 'like';
        const likeCountElement = this.querySelector('.like-count');
        const currentCount = parseInt(likeCountElement.textContent);
        
        // Set loading state
        this.classList.add('loading');
        
        try {
          const response = await fetch('/api/like', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              workId: parseInt(workId),
              action: action
            })
          });
          
          const data = await response.json();
          
          if (data.success) {
            // Update UI
            likeCountElement.textContent = data.likes;
            
            if (action === 'like') {
              this.classList.add('liked');
              this.setAttribute('aria-label', `取消点赞，当前${data.likes}个赞`);
              
              // Add to localStorage
              const likedWorks = JSON.parse(localStorage.getItem('likedWorks') || '[]');
              if (!likedWorks.includes(workId)) {
                likedWorks.push(workId);
                localStorage.setItem('likedWorks', JSON.stringify(likedWorks));
              }
            } else {
              this.classList.remove('liked');
              this.setAttribute('aria-label', `为作品点赞，当前${data.likes}个赞`);
              
              // Remove from localStorage
              const likedWorks = JSON.parse(localStorage.getItem('likedWorks') || '[]');
              const index = likedWorks.indexOf(workId);
              if (index > -1) {
                likedWorks.splice(index, 1);
                localStorage.setItem('likedWorks', JSON.stringify(likedWorks));
              }
            }
          } else {
            // Revert on error
            console.error('Like action failed:', data.error);
            
            // Show user-friendly error message
            const errorMessage = document.createElement('div');
            errorMessage.className = 'like-error';
            errorMessage.textContent = '操作失败，请稍后再试';
            errorMessage.style.cssText = `
              position: absolute;
              top: -40px;
              left: 50%;
              transform: translateX(-50%);
              background: var(--color-error);
              color: white;
              padding: 0.5rem 1rem;
              border-radius: 0.5rem;
              font-size: 0.75rem;
              white-space: nowrap;
              z-index: 1000;
            `;
            
            this.style.position = 'relative';
            this.appendChild(errorMessage);
            
            setTimeout(() => {
              errorMessage.remove();
            }, 3000);
          }
        } catch (error) {
          console.error('Network error:', error);
          
          // Show network error message
          const errorMessage = document.createElement('div');
          errorMessage.className = 'like-error';
          errorMessage.textContent = '网络错误，请检查连接';
          errorMessage.style.cssText = `
            position: absolute;
            top: -40px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--color-error);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.75rem;
            white-space: nowrap;
            z-index: 1000;
          `;
          
          this.style.position = 'relative';
          this.appendChild(errorMessage);
          
          setTimeout(() => {
            errorMessage.remove();
          }, 3000);
        } finally {
          // Remove loading state
          this.classList.remove('loading');
        }
      });
    });
  });
</script>
