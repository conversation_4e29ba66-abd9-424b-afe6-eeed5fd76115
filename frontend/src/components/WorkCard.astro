---
import type { WorkCardProps } from '@types/index';
import { strapiUtils } from '@services/strapi';
import LikeButton from './LikeButton.astro';
import ShareButton from './ShareButton.astro';

export interface Props extends WorkCardProps {}

const { work, className = '' } = Astro.props;

const imageUrl = strapiUtils.getMediaUrl(work.image);
const responsiveUrls = strapiUtils.getResponsiveImageUrls(work.image);
const shareUrl = `${Astro.site}${strapiUtils.getIssueUrl(work.issue.issueNumber)}#work-${work.id}`;
const shareTitle = `${work.photographer}的摄影作品 - FOCUS摄影周刊第${work.issue.issueNumber}期`;
---

<article class={`work-card ${className}`} id={`work-${work.id}`}>
  <!-- Work Image -->
  <div class="work-image">
    <img
      src={imageUrl}
      alt={work.image.alternativeText || `${work.photographer}的摄影作品`}
      class="image"
      loading="lazy"
      width={work.image.width}
      height={work.image.height}
      data-work-id={work.id}
      data-lightbox="work-gallery"
    />
    
    <!-- Image Overlay with Actions -->
    <div class="image-overlay">
      <button 
        class="expand-button"
        aria-label="查看大图"
        data-expand-image
        data-work-id={work.id}
      >
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7"/>
        </svg>
      </button>
    </div>
  </div>

  <!-- Work Content -->
  <div class="work-content">
    <!-- Photographer Info -->
    <div class="photographer-info">
      <h3 class="photographer-name">{work.photographer}</h3>
      {work.tags && work.tags.length > 0 && (
        <div class="tags">
          {work.tags.map((tag) => (
            <span class="tag">#{tag}</span>
          ))}
        </div>
      )}
    </div>

    <!-- Work Description -->
    {work.description && (
      <div class="work-description">
        <p>{work.description}</p>
      </div>
    )}

    <!-- Work Actions -->
    <div class="work-actions">
      <LikeButton 
        workId={work.id}
        initialLikes={work.likes}
        className="like-action"
      />
      
      <ShareButton 
        title={shareTitle}
        url={shareUrl}
        className="share-action"
      />
    </div>
  </div>
</article>

<style>
  .work-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-card);
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
  }

  .work-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card-hover);
  }

  /* Work Image */
  .work-image {
    position: relative;
    aspect-ratio: 3/2;
    overflow: hidden;
    cursor: pointer;
  }

  .image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
  }

  .work-image:hover .image {
    transform: scale(1.05);
  }

  .image-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
  }

  .work-image:hover .image-overlay {
    opacity: 1;
  }

  .expand-button {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    backdrop-filter: blur(10px);
  }

  .expand-button:hover {
    background: white;
    transform: scale(1.1);
  }

  /* Work Content */
  .work-content {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    flex: 1;
  }

  /* Photographer Info */
  .photographer-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .photographer-name {
    font-family: var(--font-serif);
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-text-primary);
    margin: 0;
  }

  .tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .tag {
    background: var(--color-divider);
    color: var(--color-text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all var(--transition-fast);
  }

  .tag:hover {
    background: var(--color-accent);
    color: white;
  }

  /* Work Description */
  .work-description {
    flex: 1;
  }

  .work-description p {
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin: 0;
    font-size: 0.875rem;
  }

  /* Work Actions */
  .work-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 1rem;
    border-top: 1px solid var(--color-divider);
    margin-top: auto;
  }

  /* Responsive */
  @media (max-width: 767px) {
    .work-content {
      padding: 1rem;
    }

    .photographer-name {
      font-size: 1rem;
    }

    .work-actions {
      flex-direction: column;
      align-items: stretch;
      gap: 0.75rem;
    }
  }

  /* Accessibility */
  @media (prefers-reduced-motion: reduce) {
    .work-card,
    .image,
    .expand-button,
    .tag {
      transition: none;
    }

    .work-card:hover {
      transform: none;
    }

    .work-image:hover .image {
      transform: none;
    }
  }

  /* Focus styles */
  .expand-button:focus {
    outline: 2px solid var(--color-accent);
    outline-offset: 2px;
  }

  /* Print styles */
  @media print {
    .image-overlay,
    .work-actions {
      display: none !important;
    }

    .work-card {
      box-shadow: none;
      border: 1px solid var(--color-border);
    }
  }
</style>

<script>
  // Image expansion functionality
  document.addEventListener('DOMContentLoaded', function() {
    const expandButtons = document.querySelectorAll('[data-expand-image]');
    
    expandButtons.forEach(button => {
      button.addEventListener('click', function() {
        const workId = this.getAttribute('data-work-id');
        const image = document.querySelector(`img[data-work-id="${workId}"]`);
        
        if (image) {
          // Dispatch custom event for image viewer
          const event = new CustomEvent('openImageViewer', {
            detail: {
              workId: workId,
              imageUrl: image.src,
              alt: image.alt
            }
          });
          document.dispatchEvent(event);
        }
      });
    });
  });
</script>
