import type { APIRoute } from 'astro';
import { issuesApi } from '@services/strapi';

export const GET: APIRoute = async () => {
  try {
    const latestIssue = await issuesApi.getLatest();

    if (!latestIssue) {
      return new Response(
        JSON.stringify({ 
          error: 'No published issues found' 
        }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        data: latestIssue
      }),
      { 
        status: 200,
        headers: { 
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=300' // Cache for 5 minutes
        }
      }
    );

  } catch (error) {
    console.error('Latest issue API error:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to fetch latest issue' 
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};

// Handle preflight requests for CORS
export const OPTIONS: APIRoute = async () => {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
};
