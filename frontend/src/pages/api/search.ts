import type { APIRoute } from 'astro';
import { issuesApi } from '@services/strapi';

export const GET: APIRoute = async ({ url }) => {
  try {
    const searchParams = new URL(url).searchParams;
    const query = searchParams.get('q');
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');

    if (!query || query.trim().length === 0) {
      return new Response(
        JSON.stringify({ 
          error: 'Search query is required' 
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if (query.trim().length < 2) {
      return new Response(
        JSON.stringify({ 
          error: 'Search query must be at least 2 characters long' 
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Search in published issues
    // Note: This is a basic implementation. In a real app, you might want to use
    // a more sophisticated search service like Elasticsearch or Algolia
    const searchResults = await issuesApi.getPublished({
      page,
      pageSize,
      filters: {
        $or: [
          { title: { $containsi: query } },
          { slug: { $containsi: query } }
        ],
        isPublished: true
      }
    });

    return new Response(
      JSON.stringify({
        success: true,
        data: searchResults.data,
        meta: {
          query: query.trim(),
          pagination: searchResults.meta.pagination
        }
      }),
      { 
        status: 200,
        headers: { 
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=60' // Cache for 1 minute
        }
      }
    );

  } catch (error) {
    console.error('Search API error:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Search failed' 
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};

// Handle preflight requests for CORS
export const OPTIONS: APIRoute = async () => {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
};
