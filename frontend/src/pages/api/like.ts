import type { APIRoute } from 'astro';
import { worksApi } from '@services/strapi';

export const POST: APIRoute = async ({ request }) => {
  try {
    const body = await request.json();
    const { workId, action } = body;

    if (!workId || !action) {
      return new Response(
        JSON.stringify({ 
          error: 'Missing required fields: workId and action' 
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if (action !== 'like' && action !== 'unlike') {
      return new Response(
        JSON.stringify({ 
          error: 'Invalid action. Must be "like" or "unlike"' 
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Get current work data
    const work = await worksApi.getById(workId);
    if (!work) {
      return new Response(
        JSON.stringify({ 
          error: 'Work not found' 
        }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Calculate new likes count
    const currentLikes = work.likes || 0;
    const newLikes = action === 'like' 
      ? currentLikes + 1 
      : Math.max(0, currentLikes - 1);

    // Update likes in Strapi
    const updatedWork = await worksApi.updateLikes(workId, newLikes);
    
    if (!updatedWork) {
      return new Response(
        JSON.stringify({ 
          error: 'Failed to update likes' 
        }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        likes: updatedWork.likes,
        action: action
      }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Like API error:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error' 
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};

// Handle preflight requests for CORS
export const OPTIONS: APIRoute = async () => {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
};
