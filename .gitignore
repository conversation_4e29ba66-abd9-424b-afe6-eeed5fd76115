# FOCUS Photography Weekly - .gitignore

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Astro specific
dist/
.astro/

# Frontend specific
frontend/dist/
frontend/.astro/
frontend/node_modules/
frontend/.env
frontend/.env.local
frontend/.env.production.local
frontend/.env.development.local

# Strapi specific
backend/.tmp/
backend/build/
backend/node_modules/
backend/.env
backend/.env.local
backend/.env.production.local
backend/.env.development.local
backend/public/uploads/*
!backend/public/uploads/.gitkeep
backend/.strapi-updater.json
backend/exports/

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# Archive files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Image processing cache
.image-cache/

# Backup files
*.bak
*.backup

# Local development
.local/
.cache/

# Testing
coverage/
.nyc_output/

# Documentation build
docs/build/

# Deployment
.vercel/
.netlify/

# Cloud storage credentials
gcloud-key.json
aws-credentials.json

# Local configuration
config.local.js
config.local.json

# Performance monitoring
.clinic/

# Webpack
.webpack/

# Rollup
.rollup.cache/

# Parcel
.parcel-cache/

# ESBuild
.esbuild/

# SWC
.swc/

# Turborepo
.turbo/

# Nx
.nx/cache
